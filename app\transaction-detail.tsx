import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Define Transaction interface
interface Transaction {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  transaction_date: string;
  profile_id?: string;
  paid_to_received_from?: string;
  linked_transaction_id?: string;
  counterparty_user_id?: string;
  is_linked?: boolean;
  created_at?: string;
  updated_at?: string;
  site_id?: string;
  is_original?: boolean;
  user_id: string;
}

export default function TransactionDetailScreen() {
  const router = useRouter();
  const { siteId, transactionId, refresh } = useLocalSearchParams();
  const insets = useSafeAreaInsets();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [autoRefreshing, setAutoRefreshing] = useState(false);
  const [showRefreshMessage, setShowRefreshMessage] = useState(false);

  // Check if current user can edit/delete this transaction
  const canModifyTransaction = transaction && currentUserId && transaction.user_id === currentUserId;

  // Debug logging for permission check
  useEffect(() => {
    if (transaction && currentUserId) {
      console.log('Permission check:', {
        transactionExists: !!transaction,
        currentUserId: currentUserId,
        transactionUserId: transaction.user_id,
        canModify: canModifyTransaction
      });
    }
  }, [transaction, currentUserId, canModifyTransaction]);

  useEffect(() => {
    console.log('TransactionDetailScreen mounted with params:', {
      siteId,
      transactionId,
      refresh,
      transactionIdType: typeof transactionId,
      transactionIdValue: transactionId
    });

    if (transactionId) {
      console.log('Starting to fetch transaction details for ID:', transactionId);
      fetchTransactionDetails();
      getCurrentUser();
    } else {
      console.error('No transactionId provided in URL parameters');
      console.error('All params received:', { siteId, transactionId, refresh });
      Alert.alert('Error', 'Transaction ID is missing from URL');
      router.back();
    }
  }, [transactionId]);

  // Auto-refresh when refresh parameter is present
  useEffect(() => {
    if (refresh === 'true') {
      // Set auto-refreshing state
      setAutoRefreshing(true);
      // Trigger refresh immediately
      fetchTransactionDetails().finally(() => {
        setAutoRefreshing(false);
        // Show refresh success message
        setShowRefreshMessage(true);
        setTimeout(() => setShowRefreshMessage(false), 2000);
      });
      // Clear the refresh parameter from URL to avoid infinite refresh
      const params = new URLSearchParams();
      if (siteId) params.append('siteId', siteId.toString());
      if (transactionId) params.append('transactionId', transactionId.toString());
      router.replace(`/transaction-detail?${params.toString()}`);
    }
  }, [refresh, siteId, transactionId]);

  // Refresh function to reload transaction data
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchTransactionDetails();
    setRefreshing(false);
  };

  const getCurrentUser = async () => {
    try {
      const { data: userData, error } = await supabase.auth.getUser();
      if (error) {
        console.error('Error getting current user:', error);
        return;
      }

      if (userData.user) {
        console.log('Setting current user ID:', userData.user.id);
        setCurrentUserId(userData.user.id);
      } else {
        console.log('No user found in auth data');
        setCurrentUserId(null);
      }
    } catch (error) {
      console.error('Error getting current user:', error);
      setCurrentUserId(null);
    }
  };

  const fetchTransactionDetails = async () => {
    try {
      setLoading(true);

      // Validate transactionId parameter
      if (!transactionId) {
        console.error('No transactionId provided');
        Alert.alert('Error', 'Transaction ID is missing');
        router.back();
        return;
      }

      console.log('Fetching transaction details for ID:', transactionId);

      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('id', transactionId)
        .single();

      if (error) {
        console.error('Error fetching transaction details:', error);
        console.error('Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        Alert.alert('Error', 'Failed to load transaction details');
        router.back();
        return;
      }

      if (!data) {
        console.error('No transaction data returned');
        Alert.alert('Error', 'Transaction not found');
        router.back();
        return;
      }

      console.log('Fetched transaction data:', {
        id: data.id,
        user_id: data.user_id,
        type: data.type,
        amount: data.amount
      });
      setTransaction(data);
    } catch (error) {
      console.error('Error in fetchTransactionDetails:', error);
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      Alert.alert('Error', 'Failed to load transaction details');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    if (!canModifyTransaction) {
      Alert.alert('Permission Denied', 'You can only edit transactions that you created.');
      return;
    }

    // Navigate to edit transaction form
    const params = new URLSearchParams();
    if (siteId) params.append('siteId', siteId.toString());
    if (transactionId) params.append('transactionId', transactionId.toString());
    params.append('mode', 'edit');
    router.push(`/transaction-form?${params.toString()}`);
  };

  const handleDelete = () => {
    if (!canModifyTransaction) {
      Alert.alert('Permission Denied', 'You can only delete transactions that you created.');
      return;
    }

    if (!transaction) {
      Alert.alert('Error', 'Transaction data not available.');
      return;
    }

    const isLinked = transaction.linked_transaction_id || transaction.counterparty_user_id;
    const deleteMessage = isLinked
      ? `⚠️ WARNING: This will delete BOTH transactions!\n\n• Your ${transaction.type} transaction (${formatCurrency(transaction.amount)})\n• The linked ${transaction.type === 'expense' ? 'income' : 'expense'} transaction for ${transaction.paid_to_received_from}\n\nThis action cannot be undone.`
      : `Are you sure you want to delete this ${transaction.type} transaction of ${formatCurrency(transaction.amount)}?\n\nThis action cannot be undone.`;

    Alert.alert(
      '🗑️ Delete Transaction',
      deleteMessage,
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: isLinked ? 'Delete Both' : 'Delete',
          style: 'destructive',
          onPress: confirmDelete
        }
      ]
    );
  };

  const confirmDelete = async () => {
    if (!transaction) return;

    // Get current user ID if not available
    let userId = currentUserId;
    if (!userId) {
      try {
        const { data: userData } = await supabase.auth.getUser();
        userId = userData.user?.id;
      } catch (error) {
        console.error('Error getting user for deletion:', error);
        Alert.alert('Error', 'Unable to verify user permissions.');
        return;
      }
    }

    if (!userId) {
      Alert.alert('Error', 'User not authenticated.');
      return;
    }

    try {
      setDeleting(true);

      // Use the secure function to delete both original and linked transactions
      const { error } = await supabase.rpc('delete_transaction_with_linked', {
        transaction_id: transaction.id,
        requesting_user_id: userId
      });

      if (error) {
        console.error('Error deleting transaction:', error);
        Alert.alert('Error', 'Failed to delete transaction. Please try again.');
        return;
      }

      const isLinked = transaction.linked_transaction_id || transaction.counterparty_user_id;
      const successMessage = isLinked
        ? '✅ Both transactions deleted successfully!\n\nYour transaction and the linked transaction for the other party have been permanently removed.'
        : '✅ Transaction deleted successfully!';

      Alert.alert('Deletion Complete', successMessage, [
        {
          text: 'OK',
          onPress: () => router.back()
        }
      ]);

    } catch (error) {
      console.error('Error in confirmDelete:', error);
      Alert.alert('Error', 'Failed to delete transaction. Please try again.');
    } finally {
      setDeleting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background, paddingTop: insets.top }]}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading transaction details...</Text>
        </View>
      </View>
    );
  }

  if (!transaction) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background, paddingTop: insets.top }]}>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: colors.textSecondary }]}>Transaction not found</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              if (transactionId) {
                fetchTransactionDetails();
              } else {
                router.back();
              }
            }}
          >
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const isIncome = transaction.type === 'income';
  const isLinked = transaction.linked_transaction_id || transaction.counterparty_user_id;

  return (
    <View style={[styles.container, { backgroundColor: colors.background, paddingTop: insets.top }]>>

      {/* Refresh Success Message */}
      {showRefreshMessage && (
        <View style={[styles.refreshMessage, { backgroundColor: colors.success }]}>
          <MaterialCommunityIcons name="check-circle" size={16} color="white" />
          <Text style={styles.refreshMessageText}>Transaction updated successfully!</Text>
        </View>
      )}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Transaction Type and Amount */}
        <View style={[styles.amountSection, { backgroundColor: colors.cardBackground }]}>
          <View style={[
            styles.typeIcon,
            { backgroundColor: isIncome ? colors.successLight : colors.dangerLight }
          ]}>
            <MaterialCommunityIcons 
              name={isIncome ? 'trending-up' : 'trending-down'} 
              size={32} 
              color={isIncome ? colors.success : colors.danger} 
            />
          </View>
          
          <Text style={[
            styles.amountText, 
            { color: isIncome ? colors.success : colors.danger }
          ]}>
            {isIncome ? '+' : '-'}{formatCurrency(transaction.amount)}
          </Text>
          
          <Text style={[styles.typeText, { color: colors.textSecondary }]}>
            {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
            {isLinked && ' • Linked Transaction'}
          </Text>
        </View>

        {/* Transaction Details */}
        <View style={[styles.detailsSection, { backgroundColor: colors.cardBackground }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Details</Text>
          
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Description</Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>{transaction.description}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>
              {isIncome ? 'From' : 'To'}
            </Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {transaction.paid_to_received_from || 'N/A'}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Date</Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {formatDate(transaction.transaction_date)}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.textSecondary }]}>Transaction ID</Text>
            <Text style={[styles.detailValue, styles.transactionIdText, { color: colors.textTertiary }]}>
              {transaction.id}
            </Text>
          </View>

          {/* Linked Transaction Info */}
          {(transaction.linked_transaction_id || transaction.counterparty_user_id) && (
            <View style={[styles.linkedTransactionInfo, { backgroundColor: colors.warningLight, borderColor: colors.warning }]}>
              <MaterialCommunityIcons name="link" size={16} color={colors.warning} />
              <View style={styles.linkedTransactionText}>
                <Text style={[styles.linkedTransactionTitle, { color: colors.warning }]}>
                  Linked Transaction
                </Text>
                <Text style={[styles.linkedTransactionDesc, { color: colors.textSecondary }]}>
                  This transaction is linked to a {transaction.type === 'expense' ? 'income' : 'expense'} transaction for {transaction.paid_to_received_from}. Deleting this will also delete the linked transaction.
                </Text>
              </View>
            </View>
          )}
        </View>

        {/* Action Buttons */}
        {!loading && canModifyTransaction && transaction && currentUserId && (
          <View style={styles.actionsSection}>
            <TouchableOpacity
              style={[styles.editButtonLarge, { backgroundColor: colors.primary }]}
              onPress={handleEdit}
            >
              <MaterialCommunityIcons name="pencil" size={20} color="white" />
              <Text style={styles.editButtonText}>Edit Transaction</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.deleteButton, { backgroundColor: colors.danger }]}
              onPress={handleDelete}
              disabled={deleting}
            >
              {deleting ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <MaterialCommunityIcons name="delete" size={20} color="white" />
              )}
              <Text style={styles.deleteButtonText}>
                {deleting ? 'Deleting...' :
                  (transaction?.linked_transaction_id || transaction?.counterparty_user_id)
                    ? 'Delete Both Transactions'
                    : 'Delete Transaction'
                }
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {!canModifyTransaction && transaction && currentUserId && (
          <View style={[styles.permissionNotice, { backgroundColor: colors.cardBackground }]}>
            <MaterialCommunityIcons name="information" size={20} color={colors.textSecondary} />
            <Text style={[styles.permissionText, { color: colors.textSecondary }]}>
              You can only edit or delete transactions that you created.
            </Text>
          </View>
        )}

        {/* Debug info - remove this after testing */}
        {!loading && transaction && currentUserId && (
          <View style={[styles.debugContainer, { backgroundColor: colors.cardBackground }]}>
            <Text style={[styles.debugText, { color: colors.textSecondary }]}>
              Debug: Current User: {currentUserId} | Transaction User: {transaction.user_id} | Can Modify: {canModifyTransaction ? 'YES' : 'NO'}
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  headerTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  headerLoader: {
    marginLeft: 8,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  refreshButton: {
    padding: 8,
    marginRight: 8,
  },
  editButton: {
    padding: 8,
    marginRight: 8,
  },
  deleteButtonHeader: {
    padding: 8,
    marginRight: -8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  amountSection: {
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  typeIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  amountText: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  typeText: {
    fontSize: 16,
  },
  detailsSection: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  detailRow: {
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  actionsSection: {
    marginBottom: 32,
  },
  editButtonLarge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  editButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
  },
  deleteButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  permissionNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 32,
  },
  permissionText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 14,
  },
  refreshMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
  },
  refreshMessageText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  linkedTransactionInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginTop: 12,
  },
  linkedTransactionText: {
    flex: 1,
    marginLeft: 8,
  },
  linkedTransactionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  linkedTransactionDesc: {
    fontSize: 12,
    lineHeight: 16,
  },
  transactionIdText: {
    fontSize: 12,
  },
  debugText: {
    fontSize: 12,
  },
  debugContainer: {
    margin: 16,
    padding: 12,
    borderRadius: 8,
  },
});
